function $ajax(obj, func,errorFunc) {
    var encryptText =''
    var encryptUp= ''
    // 参数设置
    var subData={}
    if(obj.type=='put' || obj.type=='PUT' || obj.type=='post' || obj.type=='POST'){
        // let objData = Object.assign({},obj.params)
        // for(let key  in objData){

        //     objData[key] = obj.params[key]==undefined?'':obj.params[key]
        // }
        // encryptText = JSON.stringify(objData)
        // encryptUp= sm3(encryptText)
    }else{
        if(obj.params!=undefined){
            var info =''
            for(let key  in obj.params){
                if(obj.params[key]==undefined){
                    info=info
                }else{
                    info+=obj.params[key]
                }

            }
            encryptText = info
            encryptUp= sm3(encryptText)
        }

    }
    var async = true;
    if (obj.hasOwnProperty('async')) {
        async = obj.async;
    }
    var contentType = 'application/json';
    if (obj.hasOwnProperty('contentType')) {
        contentType = obj.contentType;
    }
    var dataType = "JSON";
    if (obj.hasOwnProperty('dataType')) {
        dataType = obj.dataType;
    }
    isLogin(function (token) {

        var token = getCookie("token");
        var ulrs= ''
        if(obj.params==undefined){
            ulrs=commonUrl.baseUrl + obj.url
            subData = obj.params
        }else{
            // console.log('请求数据链接',obj.params,obj.type,obj.url)

            if(obj.type=='put' || obj.type=='PUT' || obj.type=='post' || obj.type=='POST'){
                let updates = JSON.stringify(obj.params)
                // updates = updates.substring(1,updates.length - 1);
                // console.log(updates,'json数据')
                let decData = sm4.encrypt(updates,'0123456789abcdeffedcba9876543210')
                subData.data=''
                subData.data=decData
                subData = JSON.stringify(subData)
                encryptText = sm4.decrypt(decData,'0123456789abcdeffedcba9876543210')
                encryptUp= sm3(encryptText)
                // console.log(subData,'提交数据put')
                // console.log(sm4.decrypt("adcf15f1e9fb5ca4e38790d80e637e125099452c6da291aabd9792f7b92bead9db845947cdc9cb71b79107b8761c824b22daa1de47266b2a59d63ce220cd9e339be7ce9e690b6e0bdc245cf4076cb7fb90691fa09aaebf084f6e01643ef1e75c720dd167c911078af0115e846e2dfcd38b8b3d4275a344bffb3e4565beac2a55f893311ed49aa3fd595dc0f1d91c4d4f498014ae1b07901b70f35f027f553a4f411ef5724883e2bc7b8331f0caaecfdc",'0123456789abcdeffedcba9876543210'),'解码')
            }else{
                for(let key  in obj.params){
                    var datas = obj.params[key]==undefined?'':obj.params[key]
                    datas= typeof datas=='number'?String(datas):datas
                    subData[key]=sm4.encrypt(datas,'0123456789abcdeffedcba9876543210')

                }
                // console.log(subData,'提交数据get')

            }
            let urlss=obj.url.indexOf('?')>-1?(obj.url+'&'):(obj.url+'?')
            ulrs=commonUrl.baseUrl + urlss+'encrypt='+encryptUp

        }
        $.ajax({
            type: obj.type,
            url: ulrs,
            data:subData,
            // url: commonUrl.baseUrl + obj.url,
            // data: obj.params,
            async: async,
            dataType: dataType,
            contentType: contentType,
            success: function (res) {
                var returnData = sm4.decrypt(res.Data,'0123456789abcdeffedcba9876543210')
       // console.log(returnData,'returnData')
                func && func(JSON.parse(returnData))
            },
            headers: {
                "Authorization": token,
                "X-Ip":localIp
            },
            error: function (error) {
                errorFunc&&errorFunc(error);
                if(error.responseJSON&&error.responseJSON.hasOwnProperty("code")&&error.responseJSON.code=="401"){
                    layui.layer.confirm(error.responseJSON.data, {
                        btn: ['重新登陆'], //按钮,
                        title:'提示',cancel: function(){
                            clearInterval(window.clearAllTime);
                            localStorage.clear();

                            sessionStorage.clear()
                            clearAllCookie();
                            window.location.href = 'http://**********:80/isc_sso/logout?service=http://***********:8080/ah/sgdps/index.html'
                        }
                    }, function(){
                        clearInterval(window.clearAllTime);
                        localStorage.clear();

                        sessionStorage.clear()
                        clearAllCookie();
                        window.location.href = 'http://**********:80/isc_sso/logout?service=http://***********:8080/ah/sgdps/index.html'
                    });
                }
                console.error(obj.url + '请求失败!')
            }
        });
    })

}

function getCookie(key){
    // 1.获取所有cookie
    let strC = document.cookie;
    // 2.使用"; "分隔所有的cookie，单独拿到每一条
    let arrC = strC.split("; ");
    // 3.遍历每一条cookie
    for(var i = 0;i<arrC.length;i++){
        // 4.在此使用"="分隔，分隔成 名字和值的独立的状态
        // 5.判断数组的第一位的名字时否与传进来的获取的cookie的名字一致
        if(arrC[i].split("=")[0] === key){
            // 6.如果一致，返回数组的第二位，也就是对应的值
            return arrC[i].split("=")[1];
        }
    }
    // 7.循环结束后，如果程序还在执行，说明没有找到一致的值，那就返回空字符
    return "";
}