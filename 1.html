<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>D5000_FAC_CONFIG列表</title>
    <link rel="stylesheet" href="../../../../lib/layui/css/layui.css">
    <script src="../../../../lib/jquery/jquery-2.1.4.min.js"></script>
    <script src="../../../../lib/ol3/ol.js"></script>
    <script src="../../../../lib/layui/layui.all.js"></script>
    <script src="../../../../config/config.js"></script>
    <script src="../../../../js/common/common.js"></script>
    <!--加密-->
    <script language="JavaScript" type="text/javascript" src="../../../../lib/doSM3/sm3.js"></script>
    <script language="JavaScript" type="text/javascript" src="../../../../lib/doSM3/sm4.js"></script>
    <script language="JavaScript" type="text/javascript" src="../../../../lib/doSM3/smutils.js"></script>
    <script language="JavaScript" type="text/javascript" src="../../../../lib/doSM3/byte.js"></script>
    <script src="../../../../lib/jwt/jwt.js"></script>
    <script src="../../../../lib/md5/md5.js"></script>
</head>
<style>
    .table-header-fixed {
        position: fixed;
        top: 0;
        z-index: 99;
    }

    .layui-card-body {
        padding: 0;
        margin-left: 14px;
    }

    .layui-this {
        color: #fff !important;
        background-color: #369bf7;
        font-weight: bold;
    }

    .layui-table, .layui-table-view {
        margin: 0;
    }

    /* 确保表格内容正确显示 */
    .layui-table-cell {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* 表格容器样式 */
    .table-container {
        width: 100%;
        height: 94%;
    }

    /* 需要滚动的表格容器 */
    .table-container.scrollable {
        overflow: hidden;
    }

    /* 不需要滚动的表格容器 */
    .table-container.no-scroll {
        overflow: hidden;
    }

    /* 不需要滚动的表格样式 */
    .no-scroll .layui-table-view,
    .no-scroll .layui-table-box,
    .no-scroll .layui-table-body,
    .no-scroll .layui-table {
        width: 100% !important;
    }
    .layui-body{
        height:100%!important;
    }

    .anniu {
        height: 2.8vh;
        line-height: 2.8vh;
        margin-left: 0.4vw;
        margin-bottom: 15px;
    }

    .layui-input, .layui-select, .layui-textarea {
        height: 3vh !important;
    }

    .layui-card-header{
        height: 34px;
        line-height: 45px !important;
        margin-top: 10px;
        padding: 0 !important;
    }
    .layui-form-label{
        line-height: 4px !important;
    }
    .layui-laypage button, .layui-laypage input{
        vertical-align: baseline;
    }

    .layui-input-inline{
        line-height: initial;
        margin-bottom: 6px;
    }

    .layui-laypage .layui-laypage-curr .layui-laypage-em {
        background-color: #1E9FFF !important;
    }
</style>
<body class="layui-layout-body">
    <div class="layui-body" style="left: 0;">
        <!-- 内容主体区域 -->
        <div style="padding: 0 14px;height: 96%;">
            <div class="layui-card" style="background-color: white;">
                <div class="layui-form layui-card-header">
                    <div class="layui-input-inline" id="dySelect">
                        <label class="layui-form-label" style="line-height: 30px">电压等级：</label>
                        <div class="layui-input-inline">
                            <select id="dydj" name="dydj">
                                <option value="">---请选择电压等级---</option>
                                <option value="1001">1000kV</option>
                                <option value="1003">500kV</option>
                                <option value="1005">220kV</option>
                                <option value="1006">110kV</option>
                                <option value="1008">35kV</option>
                                <option value="1009">20kV</option>
                                <option value="1010">10kV</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-input-inline">
                        <label class="layui-form-label" style="line-height: 30px">设备名称：</label>
                        <div class="layui-input-inline">
                            <input type="text" id="sbName" name="sbName" autocomplete="off" style="width: 300px"
                                   placeholder="请输入设备名称" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button id="search" type="button" class="layui-btn layui-btn-normal anniu" lay-submit lay-filter="search" style="background-color: #1E9FFF">查询</button>
                        <!--<button class="layui-btn" lay-submit id="search" lay-filter="search" style="background-color: #1E9FFF">
                            <i class="layui-icon anniu layui-icon-search"></i>
                        </button>-->
                    </div>
                </div>
            </div>
            <div id="tableContainer" class="table-container scrollable">
                <table class="layui-table" lay-filter="table"
                       style="font-size: 20px; margin: 0 0 0 10px; text-align: center;" id="table"></table>
            </div>
        </div>
    </div>

<script type="text/javascript" language="JavaScript">
    layui.use(['table', 'form', 'laydate', 'layer', 'element'], function () {
        var table = layui.table;
        var form = layui.form;
        var laydate = layui.laydate;
        var layer = layui.layer;
        var element = layui.element;

        // 定义不需要滚动条的表格类型
        var noScrollTypes = ['母线', '开关', '刀闸', '负荷'];

        // 表格列配置 - 为不同类型定义不同的列宽配置
        var colConfigs = {
            '变电站': [
                {type: 'numbers', title: '序号', align: 'center', width: 60},
                {field: 'd5000Name', title: 'd5000线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudName', title: '调控云线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudVoltagelevel', title: '电压等级', align: 'center', minWidth: 100},
                {field: 'ywdw', title: '调度机构', align: 'center', minWidth: 100},
                {field: 'type', title: '类型', align: 'center', minWidth: 100},
                {field: 'status', title: '运行状态', align: 'center', minWidth: 100},
                {field: 'operateDate', title: '投运日期', align: 'center', minWidth: 120},
                {field: 'realName', title: '全路径命名', align: 'center', minWidth: 150},
                {field: 'minFc', title: '最小分词单位', align: 'center', minWidth: 150},
            ],
            '发电厂': [
                {type: 'numbers', title: '序号', align: 'center', width: 60},
                {field: 'd5000Name', title: 'd5000线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudName', title: '调控云线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudVoltagelevel', title: '电压等级', align: 'center', minWidth: 100},
                {field: 'ywdw', title: '调度机构', align: 'center', minWidth: 100},
                {field: 'type', title: '类型', align: 'center', minWidth: 100},
                {field: 'status', title: '运行状态', align: 'center', minWidth: 100},
                {field: 'operateDate', title: '投运日期', align: 'center', minWidth: 120},
                {field: 'realName', title: '全路径命名', align: 'center', minWidth: 150},
                {field: 'minFc', title: '最小分词单位', align: 'center', minWidth: 150},
            ],
            '主变': [
                {type: 'numbers', title: '序号', align: 'center', width: 60},
                {field: 'd5000Name', title: 'd5000线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudName', title: '调控云线路名称', align: 'center', minWidth: 150},
                {field: 'd5000StName', title: '所属厂站', align: 'center', minWidth: 150},
                {field: 'vonm', title: '额定容量', align: 'center', minWidth: 100},
                {field: 'dcloudVoltagelevel', title: '电压等级', align: 'center', minWidth: 100},
                {field: 'ywdw', title: '调度机构', align: 'center', minWidth: 100},
                {field: 'status', title: '运行状态', align: 'center', minWidth: 100},
                {field: 'operateDate', title: '投运日期', align: 'center', minWidth: 120},
                {field: 'realName', title: '全路径命名', align: 'center', minWidth: 150},
                {field: 'minFc', title: '最小分词单位', align: 'center', minWidth: 150},
            ],
            // 母线表格 - 使用百分比宽度以适应屏幕
            '母线': [
                {type: 'numbers', title: '序号', align: 'center', width: 60},
                {field: 'd5000Name', title: 'd5000线路名称', align: 'center', width: '15%'},
                {field: 'dcloudName', title: '调控云线路名称', align: 'center', width: '15%'},
                {field: 'd5000StName', title: '所属厂站', align: 'center', width: '15%'},
                {field: 'dcloudVoltagelevel', title: '电压等级', align: 'center', width: '8%'},
                {field: 'ywdw', title: '调度机构', align: 'center', width: '8%'},
                {field: 'status', title: '运行状态', align: 'center', width: '8%'},
                {field: 'operateDate', title: '投运日期', align: 'center', width: '8%'},
                {field: 'realName', title: '全路径命名', align: 'center', width: '15%'},
                {field: 'minFc', title: '最小分词单位', align: 'center', width: '15%'},
            ],
            // 开关表格 - 使用百分比宽度以适应屏幕
            '开关': [
                {type: 'numbers', title: '序号', align: 'center', width: 60},
                {field: 'd5000Name', title: 'd5000线路名称', align: 'center', width: '15%'},
                {field: 'dcloudName', title: '调控云线路名称', align: 'center', width: '15%'},
                {field: 'd5000StName', title: '所属厂站', align: 'center', width: '15%'},
                {field: 'dcloudVoltagelevel', title: '电压等级', align: 'center', width: '8%'},
                {field: 'ywdw', title: '调度机构', align: 'center', width: '8%'},
                {field: 'status', title: '运行状态', align: 'center', width: '8%'},
                {field: 'operateDate', title: '投运日期', align: 'center', width: '8%'},
                {field: 'realName', title: '全路径命名', align: 'center', width: '15%'},
                {field: 'minFc', title: '最小分词单位', align: 'center', width: '15%'},
            ],
            // 刀闸表格 - 使用百分比宽度以适应屏幕
            '刀闸': [
                {type: 'numbers', title: '序号', align: 'center', width: 60},
                {field: 'd5000Name', title: 'd5000线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudName', title: '调控云线路名称', align: 'center', minWidth: 150},
                {field: 'd5000StName', title: '所属厂站', align: 'center', minWidth: 150},
                {field: 'dcloudVoltagelevel', title: '电压等级', align: 'center', minWidth: 100},
                {field: 'ywdw', title: '调度机构', align: 'center', minWidth: 100},
                {field: 'realName', title: '全路径命名', align: 'center', minWidth: 150},
                {field: 'minFc', title: '最小分词单位', align: 'center', minWidth: 150},
            ],
            '绕组': [
                {type: 'numbers', title: '序号', align: 'center', width: 60},
                {field: 'd5000Name', title: 'd5000线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudName', title: '调控云线路名称', align: 'center', minWidth: 150},
                {field: 'd5000StName', title: '所属厂站', align: 'center', minWidth: 150},
                {field: 'mvaRate', title: '额定容量', align: 'center', minWidth: 100},
                {field: 'vrate', title: '额定电压', align: 'center', minWidth: 100},
                {field: 'dcloudVoltagelevel', title: '电压等级', align: 'center', minWidth: 100},
                {field: 'ywdw', title: '调度机构', align: 'center', minWidth: 100},
                {field: 'realName', title: '全路径命名', align: 'center', minWidth: 150},
                {field: 'minFc', title: '最小分词单位', align: 'center', minWidth: 150},
            ],
            '机组': [
                {type: 'numbers', title: '序号', align: 'center', width: 60},
                {field: 'd5000Name', title: 'd5000线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudName', title: '调控云线路名称', align: 'center', minWidth: 150},
                {field: 'd5000StName', title: '所属厂站', align: 'center', minWidth: 150},
                {field: 'model', title: '型号', align: 'center', minWidth: 120},
                {field: 'mvaRate', title: '额定容量', align: 'center', minWidth: 100},
                {field: 'dcloudVoltagelevel', title: '电压等级', align: 'center', minWidth: 100},
                {field: 'ywdw', title: '调度机构', align: 'center', minWidth: 100},
                {field: 'status', title: '运行状态', align: 'center', minWidth: 100},
                {field: 'operateDate', title: '投运日期', align: 'center', minWidth: 120},
                {field: 'realName', title: '全路径命名', align: 'center', minWidth: 150},
                {field: 'minFc', title: '最小分词单位', align: 'center', minWidth: 150},
            ],
            // 负荷表格 - 使用百分比宽度以适应屏幕
            '负荷': [
                {type: 'numbers', title: '序号', align: 'center', width: 60},
                {field: 'd5000Name', title: 'd5000线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudName', title: '调控云线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudStName', title: '所属厂站', align: 'center', minWidth: 150},
                {field: 'dcloudVoltagelevel', title: '电压等级', align: 'center', minWidth: 100},
                {field: 'ywdw', title: '调度机构', align: 'center', minWidth: 100},
                {field: 'realName', title: '全路径命名', align: 'center', minWidth: 150},
                {field: 'minFc', title: '最小分词单位', align: 'center', minWidth: 150},
            ],
            '线路': [
                {type: 'numbers', title: '序号', align: 'center', width: 60},
                {field: 'd5000Name', title: 'd5000线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudName', title: '调控云线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudVoltagelevel', title: '电压等级', align: 'center', minWidth: 100},
                {field: 'd5000StartStName', title: '起始厂站', align: 'center', minWidth: 150},
                {field: 'd5000EndStName', title: '结束厂站', align: 'center', minWidth: 150},
                {field: 'model', title: '型号', align: 'center', minWidth: 120},
                {field: 'status', title: '运行状态', align: 'center', minWidth: 100},
                {field: 'length', title: '线路长度', align: 'center', minWidth: 100},
                {field: 'operateDate', title: '投运日期', align: 'center', minWidth: 120},
                {field: 'realName', title: '全路径命名', align: 'center', minWidth: 150},
                {field: 'minFc', title: '最小分词单位', align: 'center', minWidth: 150},
            ],
            'T接线路': [
                {type: 'numbers', title: '序号', align: 'center', width: 60},
                {field: 'd5000Name', title: 'd5000线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudName', title: '调控云线路名称', align: 'center', minWidth: 150},
                {field: 'dcloudVoltagelevel', title: '电压等级', align: 'center', minWidth: 100},
                {field: 'd5000StartStName', title: '起始厂站', align: 'center', minWidth: 150},
                {field: 'd5000EndStName', title: '结束厂站', align: 'center', minWidth: 150},
                {field: 'model', title: '型号', align: 'center', minWidth: 120},
                {field: 'status', title: '运行状态', align: 'center', minWidth: 100},
                {field: 'length', title: '线路长度', align: 'center', minWidth: 100},
                {field: 'operateDate', title: '投运日期', align: 'center', minWidth: 120},
                {field: 'realName', title: '全路径命名', align: 'center', minWidth: 150},
                {field: 'minFc', title: '最小分词单位', align: 'center', minWidth: 150},
            ]
        };

        // 获取URL中的type参数
        function GetUrlValue(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURIComponent(r[2]);
            return null;
        }

        // 接口配置（每个类型对应独立接口）
        var urlConfigs = {
            '变电站': commonUrl.baseUrl + 'ddgc/SubstationController/dkyBdzList',
            '发电厂': commonUrl.baseUrl + 'ddgc/PlantController/dkyFdzList',
            '主变': commonUrl.baseUrl + 'ddgc/TransformerController/dkyTrList',
            '母线': commonUrl.baseUrl + 'ddgc/BusbarController/dkyBsList',
            '开关': commonUrl.baseUrl + 'ddgc/BreakerController/dkyBrkList',
            '刀闸': commonUrl.baseUrl + 'ddgc/DisconnectController/dkyDicList',
            '绕组': commonUrl.baseUrl + 'ddgc/TransformerWindingController/dkyTrwdList',
            '机组': commonUrl.baseUrl + 'ddgc/GeneratorController/dkyJzList',
            '负荷': commonUrl.baseUrl + 'ddgc/LoadController/dkyLdList',
            '线路': commonUrl.baseUrl + 'ddgc/AclineController/dkyAclnList',
            'T接线路': commonUrl.baseUrl + 'ddgc/TlineController/dkyTAclnList'
        };

        // 渲染表格
        function renderTable(type) {
            // 清除之前的表单值
            $('#sbName').val('');
            $('#dydj').val('');

            // 重新渲染表单
            form.render('select');
            form.render();

            // 设置表格容器的类型（是否需要滚动）
            var $tableContainer = $('#tableContainer');
            if (noScrollTypes.indexOf(type) !== -1) {
                $tableContainer.removeClass('scrollable').addClass('no-scroll');
            } else {
                $tableContainer.removeClass('no-scroll').addClass('scrollable');
            }

            // 选择接口和列配置，默认为变电站
            var url = urlConfigs[type] || urlConfigs['变电站'];
            console.log('Requesting URL:', url); // 调试日志，确认请求接口

            // 获取当前类型的列配置
            var cols = [colConfigs[type] || colConfigs['变电站']];

            // 表格渲染配置
            var tableConfig = {
                elem: '#table',
                id: 'table',
                loading: true,
                method: 'post',
                limit: 50,
                url: url,
                page: true,
                defaultToolbar: [],
                height: "full-60",
                headers: {
                    Authorization: getCookie("token") || ""
                },
                cols: cols,
                done: function (res) {
                    // 表格加载完成后再次渲染表单，确保下拉框正确显示
                    form.render('select');
                    form.render();

                    // 如果是不需要滚动的表格类型，设置表格样式
                    if (noScrollTypes.indexOf(type) !== -1) {
                        // 设置表格样式以填充容器
                        $('.layui-table-view').css({
                            'width': '100%'
                        });

                        // 确保表格内容正确显示
                        $('.layui-table-cell').css({
                            'white-space': 'nowrap',
                            'overflow': 'hidden',
                            'text-overflow': 'ellipsis'
                        });
                    }
                }
            };

            // 渲染表格
            table.render(tableConfig);
        }

        // 初始化表格
        var type = GetUrlValue('type') || '变电站';
        var currentType = type;

        // 确保表单元素正确渲染
        form.render('select');
        form.render();

        // 立即渲染表格
        renderTable(type);

        // 移除重复绑定的事件
        $('.layui-nav-item').off('dblclick').off('click');

        // 监听导航链接点击事件
        $(document).on('click', '.layui-nav a[data-value]', function (e) {
            e.preventDefault();
            e.stopPropagation();
            var dataValue = $(this).data('value');
            console.log('Clicked data-value:', dataValue);
            if (dataValue) {
                var match = dataValue.match(/type=([^&]*)/);
                var newType = match ? decodeURIComponent(match[1]) : '变电站';
                console.log('Parsed type:', newType);

                if (newType !== currentType) {
                    currentType = newType;
                    renderTable(newType);
                    history.pushState({}, '', '?type=' + encodeURIComponent(newType));
                    console.log('类型已更改为:', newType);
                } else {
                    console.log('类型未变化，不重新加载数据');
                }
            }
        });

        // 监听Layui导航事件
        element.on('nav(demo)', function (elem) {
            var dataValue = elem.data('value');
            console.log('Nav event triggered, data-value:', dataValue);
            if (dataValue) {
                var match = dataValue.match(/type=([^&]*)/);
                var newType = match ? decodeURIComponent(match[1]) : '变电站';

                if (newType !== currentType) {
                    currentType = newType;
                    renderTable(newType);
                    history.pushState({}, '', '?type=' + encodeURIComponent(newType));
                    console.log('类型已更改为:', newType);
                } else {
                    console.log('类型未变化，不重新加载数据');
                }
            }
        });

        // 监听URL变化
        window.addEventListener('popstate', function () {
            var newType = GetUrlValue('type') || '变电站';
            console.log('Popstate triggered, type:', newType);

            if (newType !== currentType) {
                currentType = newType;
                renderTable(newType);
                console.log('URL变化，类型已更改为  :', newType);
            } else {
                console.log('URL变化，但类型未变化，不重新加载数据');
            }
        });

        // 搜索按钮点击事件
        form.on('submit(search)', function (data) {
            table.reload('table', {
                where: {
                    name: data.field.sbName, // 修正字段名，与表单一致
                    dydj: data.field.dydj
                },
                page: {curr: 1}
            });
            return false;
        });

        // 确保电压等级下拉框可以正常显示和选择
        $(document).ready(function () {
            setTimeout(function () {
                form.render('select');
                form.render();
            }, 100);

            // 窗口大小改变时重新调整表格
            $(window).resize(function() {
                table.resize('table');
            });
        });
    });
</script>
</body>
</html>
